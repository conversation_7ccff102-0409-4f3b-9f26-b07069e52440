@echo off
echo Building Kalonline Launcher 2025...
echo.

REM Try to find MSBuild in common locations
set MSBUILD_PATH=""

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo MSBuild not found! Please install Visual Studio 2022 or Visual Studio Build Tools.
    pause
    exit /b 1
)

echo Using MSBuild: %MSBUILD_PATH%
echo.

REM Build the solution
%MSBUILD_PATH% "Kalonline Launcher 2025.sln" /p:Configuration=Release /p:Platform="Any CPU"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo.
    echo Built files are in: Kalonline Launcher 2025\bin\Release\
    echo Main executable: Kalonline Launcher 2025.exe
    echo.
) else (
    echo.
    echo ❌ Build failed!
    echo.
)

pause
